<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;

class SettingController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        // Load settings from database with defaults
        $defaultSettings = [
            'site_name' => 'PrintOnline Ghana',
            'site_tagline' => 'On-Demand Printing & Delivery',
            'site_description' => 'The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.',
            'currency' => 'GHS',
            'timezone' => 'Africa/Accra',
            'business_name' => 'PrintOnline Ghana',
            'business_phone' => '+233 24 123 4567',
            'business_email' => '<EMAIL>',
            'business_website' => 'https://printonlinegh.com',
            'business_address' => '123 Oxford Street, Osu, Accra, Ghana',
            'mail_from_name' => 'PrintOnline Ghana',
            'mail_from_address' => '<EMAIL>',
            'email_notifications' => true,
            'enable_paystack' => false,
            'enable_momo' => false,
            'enable_bank_transfer' => true,
            'shipping_fee_accra' => '15.00',
            'shipping_fee_other' => '25.00',
            'free_shipping_threshold' => '100.00',
            'processing_time' => '3',
        ];

        // Get settings from database
        $dbSettings = Setting::pluck('value', 'key')->toArray();

        // Merge with defaults
        $settings = array_merge($defaultSettings, $dbSettings);

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $validated = $this->validateSettingsRequest($request);

        try {
            // Get all form inputs except CSRF token, method, and auto_save flag
            $inputs = $request->except(['_token', '_method', 'auto_save']);

            foreach ($inputs as $key => $value) {
                // Handle checkboxes (they won't be in the request if unchecked)
                if (in_array($key, ['email_notifications', 'enable_paystack', 'enable_momo', 'enable_bank_transfer'])) {
                    $value = $request->has($key) ? '1' : '0';
                }

                // Update or create setting
                Setting::updateOrCreate(
                    ['key' => $key],
                    [
                        'value' => $value,
                        'type' => $this->getSettingType($value),
                        'group' => $this->getSettingGroup($key),
                        'label' => $this->getSettingLabel($key),
                    ]
                );
            }

            // Return JSON response for AJAX requests (auto-save)
            if ($request->expectsJson() || $request->has('auto_save')) {
                return response()->json([
                    'success' => true,
                    'message' => $request->has('auto_save') ? 'Settings auto-saved successfully.' : 'Settings updated successfully.'
                ]);
            }

            return redirect()
                ->route('admin.settings.index')
                ->with('success', 'Settings updated successfully.');
        } catch (\Exception $e) {
            if ($request->expectsJson() || $request->has('auto_save')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update settings: ' . $e->getMessage()
                ], 422);
            }

            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to update settings: ' . $e->getMessage()]);
        }
    }

    /**
     * Validate settings request
     */
    private function validateSettingsRequest(Request $request)
    {
        return $request->validate([
            'site_name' => 'nullable|string|max:255',
            'site_tagline' => 'nullable|string|max:255',
            'site_description' => 'nullable|string',
            'currency' => 'nullable|string|in:GHS,USD,EUR',
            'timezone' => 'nullable|string',
            'business_name' => 'nullable|string|max:255',
            'business_phone' => 'nullable|string|max:20',
            'business_email' => 'nullable|email|max:255',
            'business_website' => 'nullable|url|max:255',
            'business_address' => 'nullable|string',
            'mail_from_name' => 'nullable|string|max:255',
            'mail_from_address' => 'nullable|email|max:255',
            'email_notifications' => 'boolean',
            'enable_paystack' => 'boolean',
            'enable_momo' => 'boolean',
            'enable_bank_transfer' => 'boolean',
            'shipping_fee_accra' => 'nullable|numeric|min:0',
            'shipping_fee_other' => 'nullable|numeric|min:0',
            'free_shipping_threshold' => 'nullable|numeric|min:0',
            'processing_time' => 'nullable|integer|min:1',
        ]);
    }

    /**
     * Get the type of a setting value
     */
    private function getSettingType($value): string
    {
        if (is_bool($value) || in_array($value, ['0', '1', 'true', 'false'])) {
            return 'boolean';
        }

        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? 'float' : 'integer';
        }

        return 'string';
    }

    /**
     * Get the group for a setting key
     */
    private function getSettingGroup(string $key): string
    {
        if (str_starts_with($key, 'business_')) {
            return 'business';
        }

        if (str_starts_with($key, 'mail_') || str_contains($key, 'email')) {
            return 'email';
        }

        if (str_contains($key, 'payment') || str_contains($key, 'paystack') || str_contains($key, 'momo')) {
            return 'payment';
        }

        if (str_contains($key, 'shipping') || str_contains($key, 'processing')) {
            return 'shipping';
        }

        return 'general';
    }

    /**
     * Get a human-readable label for a setting key
     */
    private function getSettingLabel(string $key): string
    {
        return ucwords(str_replace('_', ' ', $key));
    }
}
