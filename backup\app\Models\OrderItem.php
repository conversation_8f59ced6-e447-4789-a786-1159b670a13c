<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class OrderItem extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'selected_options',
        'quantity',
        'unit_price',
        'total_price',
        'design_file_path',
    ];

    protected $casts = [
        'selected_options' => 'array',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * Get the order that owns this item
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the product
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the formatted unit price
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return '₵' . number_format($this->unit_price, 2);
    }

    /**
     * Get the formatted total price
     */
    public function getFormattedTotalPriceAttribute(): string
    {
        return '₵' . number_format($this->total_price, 2);
    }

    /**
     * Get the selected options as a formatted string
     */
    public function getFormattedOptionsAttribute(): string
    {
        if (empty($this->selected_options)) {
            return '';
        }

        $options = [];
        foreach ($this->selected_options as $key => $value) {
            $options[] = "{$key}: {$value}";
        }

        return implode(', ', $options);
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('design_files')
            ->acceptsMimeTypes([
                'image/jpeg', 'image/png', 'image/gif', 'image/webp',
                'application/pdf', 'image/svg+xml'
            ]);
    }

    /**
     * Register media conversions
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('preview')
            ->width(200)
            ->height(200)
            ->sharpen(10)
            ->performOnCollections('design_files');
    }

    /**
     * Get the design file URL
     */
    public function getDesignFileUrlAttribute(): ?string
    {
        $media = $this->getFirstMedia('design_files');
        return $media ? $media->getUrl() : null;
    }

    /**
     * Get the design file preview URL
     */
    public function getDesignFilePreviewUrlAttribute(): ?string
    {
        $media = $this->getFirstMedia('design_files');
        return $media ? $media->getUrl('preview') : null;
    }
}
