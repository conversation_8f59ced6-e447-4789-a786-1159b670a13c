<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_option_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_option_id')->constrained()->onDelete('cascade');
            $table->string('name'); // e.g., "S", "Matte", "x100"
            $table->string('value')->nullable(); // For colors, e.g., "#FFFFFF"
            $table->decimal('price', 10, 2)->default(0); // Base price for this variation
            $table->decimal('multiplier', 8, 2)->default(1); // For quantity packs
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['product_option_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_option_values');
    }
};
