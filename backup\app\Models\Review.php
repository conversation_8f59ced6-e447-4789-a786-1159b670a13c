<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Review extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_name',
        'avatar_initials',
        'review_text',
        'rating',
        'is_featured',
        'is_active',
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'rating' => 'integer',
    ];

    /**
     * Scope a query to only include active reviews.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured reviews.
     */
    public function scopeFeatured(Builder $query): void
    {
        $query->where('is_featured', true);
    }

    /**
     * Scope a query to filter by rating.
     */
    public function scopeRating(Builder $query, int $rating): void
    {
        $query->where('rating', $rating);
    }

    /**
     * Get the star rating as HTML
     */
    public function getStarRatingHtmlAttribute(): string
    {
        $html = '';
        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $this->rating) {
                $html .= '<i class="fas fa-star text-yellow-400"></i>';
            } else {
                $html .= '<i class="far fa-star text-gray-300"></i>';
            }
        }
        return $html;
    }

    /**
     * Get the avatar background color based on initials
     */
    public function getAvatarColorAttribute(): string
    {
        $colors = [
            '#E9D5FF', '#BBF7D0', '#FECACA', '#FED7AA', '#BFDBFE',
            '#C7D2FE', '#F3E8FF', '#D1FAE5', '#FEE2E2', '#FECDD3'
        ];
        
        $index = ord($this->avatar_initials[0] ?? 'A') % count($colors);
        return $colors[$index];
    }

    /**
     * Get the avatar text color based on background
     */
    public function getAvatarTextColorAttribute(): string
    {
        $textColors = [
            '#4C1D95', '#14532D', '#7F1D1D', '#9A3412', '#1E3A8A',
            '#3730A3', '#581C87', '#065F46', '#991B1B', '#BE185D'
        ];
        
        $index = ord($this->avatar_initials[0] ?? 'A') % count($textColors);
        return $textColors[$index];
    }
}
