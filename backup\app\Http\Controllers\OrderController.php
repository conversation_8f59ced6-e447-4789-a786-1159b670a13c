<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Product;
use App\Services\ProductPricingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    protected ProductPricingService $pricingService;

    public function __construct(ProductPricingService $pricingService)
    {
        $this->pricingService = $pricingService;
    }

    /**
     * Display user's orders
     */
    public function index()
    {
        $orders = Auth::user()
            ->orders()
            ->with(['items.product'])
            ->recent()
            ->paginate(10);

        return view('orders.index', compact('orders'));
    }

    /**
     * Display a specific order
     */
    public function show(Order $order)
    {
        // Ensure user can only view their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        $order->load(['items.product']);

        return view('orders.show', compact('order'));
    }

    /**
     * Store a new order
     */
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'options' => 'required|array',
            'options.*' => 'required|integer|exists:product_option_values,id',
            'quantity' => 'integer|min:1|max:100',
            'design_file' => 'nullable|file|mimes:jpeg,png,gif,pdf,svg|max:10240', // 10MB max
        ]);

        try {
            DB::beginTransaction();

            $product = Product::findOrFail($request->product_id);
            $quantity = $request->get('quantity', 1);

            // Validate options
            $errors = $this->pricingService->validateOptions($product, $request->options);
            if (!empty($errors)) {
                return back()->withErrors($errors)->withInput();
            }

            // Calculate pricing
            $orderItemTotal = $this->pricingService->calculateOrderItemTotal(
                $product,
                $request->options,
                $quantity
            );

            // Create order
            $order = Order::create([
                'user_id' => Auth::id(),
                'total_amount' => $orderItemTotal['total_price'],
                'status' => 'pending',
            ]);

            // Create order item
            $orderItem = $order->items()->create([
                'product_id' => $product->id,
                'product_name' => $product->name,
                'selected_options' => $this->formatSelectedOptions($product, $request->options),
                'quantity' => $quantity,
                'unit_price' => $orderItemTotal['unit_price'],
                'total_price' => $orderItemTotal['total_price'],
            ]);

            // Handle file upload if present
            if ($request->hasFile('design_file')) {
                $orderItem->addMediaFromRequest('design_file')
                    ->toMediaCollection('design_files');
            }

            DB::commit();

            return redirect()
                ->route('orders.show', $order)
                ->with('success', 'Order placed successfully! Order #' . $order->order_number);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()
                ->withErrors(['error' => 'Failed to place order. Please try again.'])
                ->withInput();
        }
    }

    /**
     * Format selected options for storage
     */
    private function formatSelectedOptions(Product $product, array $selectedOptions): array
    {
        $product->load(['options.values']);
        $formatted = [];

        foreach ($selectedOptions as $optionId => $valueId) {
            $option = $product->options->find($optionId);
            $value = $option?->values->find($valueId);

            if ($option && $value) {
                $formatted[$option->name] = $value->name;
            }
        }

        return $formatted;
    }
}
