<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'Admin Dashboard' }} - PrintOnline Ghana</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Lato:wght@400;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    @stack('styles')

    <style>
        body {
            font-family: 'Lato', sans-serif;
            background-color: #f1f5f9;
        }
        h1, h2, h3, h4, h5, h6, .font-heading {
            font-family: 'Poppins', sans-serif;
        }
        .sidebar-link {
            transition: background-color 0.2s, color 0.2s;
        }
        .sidebar-link.active, .sidebar-link:hover {
            background-color: #fb7185;
            color: white;
        }
    </style>
</head>
<body class="flex h-screen">
    <!-- Sidebar -->
    <aside class="w-64 bg-slate-800 text-white flex flex-col">
        <!-- Logo -->
        <div class="flex items-center justify-center p-6 border-b border-slate-700">
            <img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" 
                 alt="Logo" class="h-10 filter grayscale brightness-0 invert">
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2">
            <a href="{{ route('admin.dashboard') }}" 
               class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                <i class="fas fa-tachometer-alt w-6 text-center"></i>
                <span class="font-semibold">Dashboard</span>
            </a>
            
            <a href="{{ route('admin.orders.index') }}" 
               class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg {{ request()->routeIs('admin.orders.*') ? 'active' : '' }}">
                <i class="fas fa-shopping-cart w-6 text-center"></i>
                <span class="font-semibold">Orders</span>
            </a>
            
            <a href="{{ route('admin.products.index') }}" 
               class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg {{ request()->routeIs('admin.products.*') ? 'active' : '' }}">
                <i class="fas fa-box w-6 text-center"></i>
                <span class="font-semibold">Products</span>
            </a>
            
            <a href="{{ route('admin.users.index') }}" 
               class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                <i class="fas fa-users w-6 text-center"></i>
                <span class="font-semibold">Users</span>
            </a>
            
            <a href="{{ route('admin.settings.index') }}" 
               class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
                <i class="fas fa-cog w-6 text-center"></i>
                <span class="font-semibold">Settings</span>
            </a>
        </nav>

        <!-- User Info & Logout -->
        <div class="p-6 border-t border-slate-700">
            <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-sm"></i>
                </div>
                <div>
                    <p class="text-sm font-semibold">{{ auth()->user()->name }}</p>
                    <p class="text-xs text-slate-400">Administrator</p>
                </div>
            </div>
            
            <div class="flex gap-2">
                <a href="{{ route('home') }}" 
                   class="flex-1 text-center py-2 px-3 bg-slate-700 rounded text-xs hover:bg-slate-600 transition">
                    <i class="fas fa-home mr-1"></i>Site
                </a>
                <form method="POST" action="{{ route('logout') }}" class="flex-1">
                    @csrf
                    <button type="submit" 
                            class="w-full py-2 px-3 bg-slate-700 rounded text-xs hover:bg-slate-600 transition">
                        <i class="fas fa-sign-out-alt mr-1"></i>Logout
                    </button>
                </form>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-6 lg:p-10 overflow-y-auto">
        <!-- Page Header -->
        @if(isset($pageTitle))
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-slate-800">{{ $pageTitle }}</h1>
                @if(isset($pageDescription))
                    <p class="text-slate-600 mt-2">{{ $pageDescription }}</p>
                @endif
            </div>
        @endif

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if(session('error'))
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        @if($errors->any())
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                <ul class="list-disc list-inside">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Page Content -->
        {{ $slot }}
    </main>

    @stack('scripts')
</body>
</html>
