/**
 * Product Manager Module
 * Handles product options, option values, and related functionality
 * Uses CDN libraries: GSAP, SortableJS, and SweetAlert2
 */
class ProductManager {
    constructor() {
        this.optionIndex = 0;
        this.init();
    }

    init() {
        if (this.isProductPage()) {
            // Wait for CDN libraries to load
            this.waitForLibraries().then(() => {
                this.setupProductOptions();
                this.setupImageUpload();
                this.setupFormValidation();
                this.loadExistingOptions();
            });
        }
    }

    /**
     * Wait for CDN libraries to be available
     */
    async waitForLibraries() {
        const checkLibraries = () => {
            return typeof gsap !== 'undefined' &&
                   typeof Sortable !== 'undefined' &&
                   typeof Swal !== 'undefined';
        };

        if (checkLibraries()) {
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            const interval = setInterval(() => {
                if (checkLibraries()) {
                    clearInterval(interval);
                    resolve();
                }
            }, 100);
        });
    }

    /**
     * Check if we're on a product add/edit page
     */
    isProductPage() {
        return document.getElementById('options-container') !== null;
    }

    /**
     * Setup product options functionality
     */
    setupProductOptions() {
        const container = document.getElementById('options-container');
        if (!container) return;

        // Make options sortable
        this.setupSortableOptions();

        // Setup add option button
        const addButton = document.querySelector('[onclick="addOption()"]');
        if (addButton) {
            addButton.removeAttribute('onclick');
            addButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.addOption();
            });
        }

        // Setup global functions for backward compatibility
        window.addOption = () => this.addOption();
        window.removeOption = (button) => this.removeOption(button);
        window.addOptionValue = (button, optionIndex) => this.addOptionValue(button, optionIndex);
        window.removeOptionValue = (button) => this.removeOptionValue(button);
    }

    /**
     * Setup sortable options
     */
    setupSortableOptions() {
        const container = document.getElementById('options-container');
        if (!container) return;

        Sortable.create(container, {
            handle: '.option-drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onEnd: () => {
                this.updateOptionIndices();
            }
        });
    }

    /**
     * Add new product option
     */
    addOption() {
        const container = document.getElementById('options-container');
        if (!container) return;

        // Remove empty state if present
        const emptyState = container.querySelector('.text-center');
        if (emptyState) {
            gsap.to(emptyState, {
                duration: 0.3,
                opacity: 0,
                y: -20,
                ease: 'power2.in',
                onComplete: () => emptyState.remove()
            });
        }

        const optionHtml = this.getOptionTemplate(this.optionIndex);
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = optionHtml;
        const optionElement = tempDiv.firstElementChild;

        container.appendChild(optionElement);

        // Animate in
        gsap.from(optionElement, {
            duration: 0.6,
            y: 30,
            opacity: 0,
            ease: 'power2.out'
        });

        // Setup sortable for option values
        this.setupSortableValues(optionElement);

        // Setup event handlers for the new option
        this.setupOptionEventHandlers(optionElement, this.optionIndex);

        this.optionIndex++;
    }

    /**
     * Remove product option
     */
    removeOption(button) {
        const optionGroup = button.closest('.option-group');
        if (!optionGroup) return;

        Swal.fire({
            title: 'Remove Option?',
            text: 'This will remove the option and all its values.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, remove it!'
        }).then((result) => {
            if (result.isConfirmed) {
                gsap.to(optionGroup, {
                    duration: 0.4,
                    x: -100,
                    opacity: 0,
                    ease: 'power2.in',
                    onComplete: () => {
                        optionGroup.remove();
                        this.checkEmptyState();
                        this.updateOptionIndices();
                    }
                });
            }
        });
    }

    /**
     * Add option value
     */
    addOptionValue(button, optionIndex) {
        const container = button.previousElementSibling;
        if (!container) return;

        const valueIndex = container.children.length;
        const valueHtml = this.getValueTemplate(optionIndex, valueIndex);

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = valueHtml;
        const valueElement = tempDiv.firstElementChild;

        container.appendChild(valueElement);

        // Setup event handler for the remove button
        const removeButton = valueElement.querySelector('.remove-value-btn');
        if (removeButton) {
            removeButton.addEventListener('click', () => this.removeOptionValue(removeButton));
        }

        // Animate in
        gsap.from(valueElement, {
            duration: 0.4,
            x: 20,
            opacity: 0,
            ease: 'power2.out'
        });
    }

    /**
     * Remove option value
     */
    removeOptionValue(button) {
        const valueElement = button.closest('.option-value-row');
        if (!valueElement) return;

        gsap.to(valueElement, {
            duration: 0.3,
            x: 100,
            opacity: 0,
            ease: 'power2.in',
            onComplete: () => valueElement.remove()
        });
    }

    /**
     * Setup sortable for option values
     */
    setupSortableValues(optionElement) {
        const valuesContainer = optionElement.querySelector('.values-container');
        if (!valuesContainer) return;

        Sortable.create(valuesContainer, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag'
        });
    }

    /**
     * Check if we need to show empty state
     */
    checkEmptyState() {
        const container = document.getElementById('options-container');
        if (!container || container.children.length > 0) return;

        const emptyStateHtml = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-plus-circle text-4xl mb-4"></i>
                <p>Click "Add Option" to create product variations like size, color, material, etc.</p>
            </div>
        `;

        container.innerHTML = emptyStateHtml;
        
        gsap.from(container.firstElementChild, {
            duration: 0.6,
            y: 20,
            opacity: 0,
            ease: 'power2.out'
        });
    }

    /**
     * Update option indices after reordering
     */
    updateOptionIndices() {
        const options = document.querySelectorAll('.option-group');
        options.forEach((option, index) => {
            // Update all input names with new index
            const inputs = option.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.name) {
                    input.name = input.name.replace(/\[\d+\]/, `[${index}]`);
                }
            });
        });
    }

    /**
     * Load existing options for edit page
     */
    loadExistingOptions() {
        const existingOptions = document.querySelectorAll('.option-group');
        existingOptions.forEach((option, index) => {
            this.setupSortableValues(option);
            this.optionIndex = Math.max(this.optionIndex, index + 1);

            // Ensure existing options have proper event handlers
            this.setupOptionEventHandlers(option, index);
        });

        // Update the global option index to continue from the last existing option
        this.optionIndex = existingOptions.length;
    }

    /**
     * Setup event handlers for existing options
     */
    setupOptionEventHandlers(optionElement, optionIndex) {
        // Setup remove button (try both onclick and class selectors)
        let removeButton = optionElement.querySelector('.remove-option-btn');
        if (!removeButton) {
            removeButton = optionElement.querySelector('button[onclick*="removeOption"]');
            if (removeButton) {
                removeButton.removeAttribute('onclick');
                removeButton.classList.add('remove-option-btn');
            }
        }
        if (removeButton) {
            removeButton.addEventListener('click', () => this.removeOption(removeButton));
        }

        // Setup add value button (try both onclick and class selectors)
        let addValueButton = optionElement.querySelector('.add-value-btn');
        if (!addValueButton) {
            addValueButton = optionElement.querySelector('button[onclick*="addOptionValue"]');
            if (addValueButton) {
                addValueButton.removeAttribute('onclick');
                addValueButton.classList.add('add-value-btn');
            }
        }
        if (addValueButton) {
            addValueButton.addEventListener('click', () => this.addOptionValue(addValueButton, optionIndex));
        }

        // Setup remove value buttons (try both onclick and class selectors)
        let removeValueButtons = optionElement.querySelectorAll('.remove-value-btn');
        if (removeValueButtons.length === 0) {
            removeValueButtons = optionElement.querySelectorAll('button[onclick*="removeOptionValue"]');
            removeValueButtons.forEach(button => {
                button.removeAttribute('onclick');
                button.classList.add('remove-value-btn');
            });
        }
        removeValueButtons.forEach(button => {
            button.addEventListener('click', () => this.removeOptionValue(button));
        });
    }

    /**
     * Setup image upload functionality
     */
    setupImageUpload() {
        const imageInput = document.querySelector('input[type="file"][name="image"]');
        if (!imageInput) return;

        imageInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            // Validate file type
            if (!file.type.startsWith('image/')) {
                window.showToast('error', 'Please select a valid image file');
                e.target.value = '';
                return;
            }

            // Validate file size (max 2MB)
            if (file.size > 2 * 1024 * 1024) {
                window.showToast('error', 'Image size must be less than 2MB');
                e.target.value = '';
                return;
            }

            // Show preview
            this.showImagePreview(file);
        });
    }

    /**
     * Show image preview
     */
    showImagePreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            let preview = document.querySelector('.image-preview');
            if (!preview) {
                preview = document.createElement('div');
                preview.className = 'image-preview mt-4';
                document.querySelector('input[type="file"][name="image"]').parentNode.appendChild(preview);
            }

            preview.innerHTML = `
                <div class="relative inline-block">
                    <img src="${e.target.result}" alt="Preview" class="w-32 h-32 object-cover rounded-lg border">
                    <button type="button" onclick="this.parentElement.parentElement.remove()" 
                            class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                        ×
                    </button>
                </div>
            `;

            gsap.from(preview, {
                duration: 0.4,
                scale: 0.8,
                opacity: 0,
                ease: 'power2.out'
            });
        };
        reader.readAsDataURL(file);
    }

    /**
     * Setup form validation
     */
    setupFormValidation() {
        const form = document.querySelector('form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            if (!this.validateProductForm()) {
                e.preventDefault();
            }
        });
    }

    /**
     * Validate product form
     */
    validateProductForm() {
        let isValid = true;
        const errors = [];

        // Validate basic fields
        const requiredFields = ['name', 'description', 'category'];
        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field && !field.value.trim()) {
                errors.push(`${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`);
                isValid = false;
            }
        });

        // Validate options
        const options = document.querySelectorAll('.option-group');
        options.forEach((option, index) => {
            const nameInput = option.querySelector('[name*="[name]"]');
            if (nameInput && !nameInput.value.trim()) {
                errors.push(`Option ${index + 1} name is required`);
                isValid = false;
            }

            const values = option.querySelectorAll('.option-value-row');
            if (values.length === 0) {
                errors.push(`Option ${index + 1} must have at least one value`);
                isValid = false;
            }
        });

        if (!isValid) {
            window.showToast('error', 'Please fix the following errors: ' + errors.join(', '));
        }

        return isValid;
    }

    /**
     * Get option template HTML
     */
    getOptionTemplate(optionIndex) {
        return `
            <div class="option-group bg-gray-50 rounded-lg p-4 mb-4 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-2">
                        <div class="option-drag-handle cursor-move text-gray-400 hover:text-gray-600">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Option ${optionIndex + 1}</h4>
                    </div>
                    <button type="button" class="remove-option-btn text-red-600 hover:text-red-800 transition">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Option Name</label>
                        <input type="text" name="options[${optionIndex}][name]"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                               placeholder="e.g., Size, Color, Material" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Option Type</label>
                        <select name="options[${optionIndex}][type]"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                            <option value="button">Button Selection</option>
                            <option value="color">Color Picker</option>
                            <option value="quantity">Quantity Pack</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Required</label>
                        <select name="options[${optionIndex}][required]"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                            <option value="1">Yes</option>
                            <option value="0">No</option>
                        </select>
                    </div>
                </div>

                <div class="option-values">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Option Values</label>
                    <div class="values-container space-y-2"></div>
                    <button type="button" class="add-value-btn mt-2 px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition text-sm">
                        <i class="fas fa-plus mr-1"></i>Add Value
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Get value template HTML
     */
    getValueTemplate(optionIndex, valueIndex) {
        return `
            <div class="option-value-row flex items-center gap-2">
                <input type="text" name="options[${optionIndex}][values][${valueIndex}][name]"
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                       placeholder="Value name" required>
                <input type="text" name="options[${optionIndex}][values][${valueIndex}][value]"
                       class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                       placeholder="Color/Value">
                <input type="number" name="options[${optionIndex}][values][${valueIndex}][price]"
                       class="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                       placeholder="0.00" step="0.01" min="0">
                <input type="number" name="options[${optionIndex}][values][${valueIndex}][multiplier]"
                       class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                       placeholder="1" step="0.01" value="1" min="0.01">
                <button type="button" class="remove-value-btn text-red-600 hover:text-red-800 transition">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    }
}

// Initialize product manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.ProductManager = new ProductManager();
});

export default ProductManager;
