<x-layouts.admin title="CDN Library Test">
    <x-slot name="pageTitle">CDN Library Test</x-slot>
    <x-slot name="pageDescription">Test page to verify CDN libraries are loading correctly</x-slot>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- GSAP Test -->
        <div class="bg-white rounded-xl shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">GSAP Animation Test</h3>
            <div id="gsap-test-box" class="w-16 h-16 bg-pink-500 rounded-lg mb-4"></div>
            <button onclick="testGSAP()" class="px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition">
                Test GSAP Animation
            </button>
            <p id="gsap-status" class="mt-2 text-sm text-gray-600">Status: Not tested</p>
        </div>

        <!-- SortableJS Test -->
        <div class="bg-white rounded-xl shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">SortableJS Test</h3>
            <ul id="sortable-test" class="space-y-2 mb-4">
                <li class="p-2 bg-gray-100 rounded cursor-move">Item 1</li>
                <li class="p-2 bg-gray-100 rounded cursor-move">Item 2</li>
                <li class="p-2 bg-gray-100 rounded cursor-move">Item 3</li>
            </ul>
            <button onclick="testSortable()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                Enable Sorting
            </button>
            <p id="sortable-status" class="mt-2 text-sm text-gray-600">Status: Not tested</p>
        </div>

        <!-- SweetAlert2 Test -->
        <div class="bg-white rounded-xl shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">SweetAlert2 Test</h3>
            <div class="space-y-2 mb-4">
                <button onclick="testSwalSuccess()" class="block w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition">
                    Test Success Alert
                </button>
                <button onclick="testSwalError()" class="block w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition">
                    Test Error Alert
                </button>
                <button onclick="testSwalConfirm()" class="block w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition">
                    Test Confirm Dialog
                </button>
            </div>
            <p id="swal-status" class="mt-2 text-sm text-gray-600">Status: Not tested</p>
        </div>
    </div>

    <!-- Library Status -->
    <div class="mt-8 bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Library Loading Status</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="flex items-center gap-2">
                <span id="gsap-indicator" class="w-3 h-3 bg-red-500 rounded-full"></span>
                <span>GSAP: <span id="gsap-version">Not loaded</span></span>
            </div>
            <div class="flex items-center gap-2">
                <span id="sortable-indicator" class="w-3 h-3 bg-red-500 rounded-full"></span>
                <span>SortableJS: <span id="sortable-version">Not loaded</span></span>
            </div>
            <div class="flex items-center gap-2">
                <span id="swal-indicator" class="w-3 h-3 bg-red-500 rounded-full"></span>
                <span>SweetAlert2: <span id="swal-version">Not loaded</span></span>
            </div>
        </div>
    </div>
</x-layouts.admin>

@push('scripts')
<script>
// Check library loading status
document.addEventListener('DOMContentLoaded', function() {
    checkLibraryStatus();
});

function checkLibraryStatus() {
    // Check GSAP
    if (typeof gsap !== 'undefined') {
        document.getElementById('gsap-indicator').className = 'w-3 h-3 bg-green-500 rounded-full';
        document.getElementById('gsap-version').textContent = 'Loaded (v' + gsap.version + ')';
    }

    // Check SortableJS
    if (typeof Sortable !== 'undefined') {
        document.getElementById('sortable-indicator').className = 'w-3 h-3 bg-green-500 rounded-full';
        document.getElementById('sortable-version').textContent = 'Loaded';
    }

    // Check SweetAlert2
    if (typeof Swal !== 'undefined') {
        document.getElementById('swal-indicator').className = 'w-3 h-3 bg-green-500 rounded-full';
        document.getElementById('swal-version').textContent = 'Loaded (v' + Swal.version + ')';
    }
}

function testGSAP() {
    if (typeof gsap === 'undefined') {
        document.getElementById('gsap-status').textContent = 'Status: GSAP not loaded!';
        document.getElementById('gsap-status').className = 'mt-2 text-sm text-red-600';
        return;
    }

    const box = document.getElementById('gsap-test-box');
    gsap.to(box, {
        duration: 1,
        rotation: 360,
        scale: 1.2,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
    });

    document.getElementById('gsap-status').textContent = 'Status: Animation completed!';
    document.getElementById('gsap-status').className = 'mt-2 text-sm text-green-600';
}

function testSortable() {
    if (typeof Sortable === 'undefined') {
        document.getElementById('sortable-status').textContent = 'Status: SortableJS not loaded!';
        document.getElementById('sortable-status').className = 'mt-2 text-sm text-red-600';
        return;
    }

    const list = document.getElementById('sortable-test');
    Sortable.create(list, {
        animation: 150,
        ghostClass: 'opacity-50'
    });

    document.getElementById('sortable-status').textContent = 'Status: Sorting enabled! Try dragging items.';
    document.getElementById('sortable-status').className = 'mt-2 text-sm text-green-600';
}

function testSwalSuccess() {
    if (typeof Swal === 'undefined') {
        document.getElementById('swal-status').textContent = 'Status: SweetAlert2 not loaded!';
        document.getElementById('swal-status').className = 'mt-2 text-sm text-red-600';
        return;
    }

    Swal.fire({
        title: 'Success!',
        text: 'SweetAlert2 is working perfectly!',
        icon: 'success',
        confirmButtonText: 'Great!'
    });

    document.getElementById('swal-status').textContent = 'Status: Success alert shown!';
    document.getElementById('swal-status').className = 'mt-2 text-sm text-green-600';
}

function testSwalError() {
    if (typeof Swal === 'undefined') {
        return;
    }

    Swal.fire({
        title: 'Error!',
        text: 'This is a test error message.',
        icon: 'error',
        confirmButtonText: 'OK'
    });
}

function testSwalConfirm() {
    if (typeof Swal === 'undefined') {
        return;
    }

    Swal.fire({
        title: 'Are you sure?',
        text: "This is a test confirmation dialog.",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, proceed!'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire(
                'Confirmed!',
                'You clicked the confirm button.',
                'success'
            );
        }
    });
}
</script>
@endpush
