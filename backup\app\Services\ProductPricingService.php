<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductOption;
use App\Models\ProductOptionValue;
use Illuminate\Support\Collection;

class ProductPricingService
{
    /**
     * Calculate the price for a product with selected options
     * 
     * This replicates the JavaScript updatePrice() function logic
     */
    public function calculatePrice(Product $product, array $selectedOptions): array
    {
        $currentPrice = 0;
        $multiplier = 1;
        $breakdown = [];

        // Load product with options and values
        $product->load(['options.values']);

        foreach ($selectedOptions as $optionId => $valueId) {
            $option = $product->options->find($optionId);
            
            if (!$option) {
                continue;
            }

            $value = $option->values->find($valueId);
            
            if (!$value) {
                continue;
            }

            // Store breakdown information
            $breakdown[] = [
                'option_name' => $option->name,
                'value_name' => $value->name,
                'price' => $value->price,
                'multiplier' => $value->multiplier,
                'type' => $option->type,
            ];

            // Apply pricing logic based on option type
            if ($option->type === 'quantity') {
                $multiplier = $value->multiplier;
            } else {
                $currentPrice += $value->price;
            }
        }

        $finalPrice = $currentPrice * $multiplier;

        return [
            'base_price' => $currentPrice,
            'multiplier' => $multiplier,
            'final_price' => $finalPrice,
            'formatted_price' => '₵' . number_format($finalPrice, 2),
            'breakdown' => $breakdown,
        ];
    }

    /**
     * Get the default price for a product (first option of each type)
     */
    public function getDefaultPrice(Product $product): array
    {
        $product->load(['options.values']);
        
        $defaultOptions = [];
        
        foreach ($product->options as $option) {
            $firstValue = $option->values->first();
            if ($firstValue) {
                $defaultOptions[$option->id] = $firstValue->id;
            }
        }

        return $this->calculatePrice($product, $defaultOptions);
    }

    /**
     * Get the starting price for a product (minimum possible price)
     */
    public function getStartingPrice(Product $product): float
    {
        $product->load(['options.values']);
        
        $minPrice = 0;
        $minMultiplier = 1;

        foreach ($product->options as $option) {
            if ($option->type === 'quantity') {
                $minMultiplier = $option->values->min('multiplier') ?? 1;
            } else {
                $minPrice += $option->values->min('price') ?? 0;
            }
        }

        return $minPrice * $minMultiplier;
    }

    /**
     * Validate selected options for a product
     */
    public function validateOptions(Product $product, array $selectedOptions): array
    {
        $errors = [];
        $product->load(['options.values']);

        // Check if all required options are selected
        foreach ($product->options as $option) {
            if (!isset($selectedOptions[$option->id])) {
                $errors[] = "Please select a {$option->name}";
                continue;
            }

            $valueId = $selectedOptions[$option->id];
            $value = $option->values->find($valueId);

            if (!$value) {
                $errors[] = "Invalid {$option->name} selection";
            }
        }

        return $errors;
    }

    /**
     * Get all possible price combinations for a product
     */
    public function getAllPriceCombinations(Product $product): Collection
    {
        $product->load(['options.values']);
        
        $combinations = collect();
        $optionCombinations = $this->generateOptionCombinations($product->options);

        foreach ($optionCombinations as $combination) {
            $pricing = $this->calculatePrice($product, $combination);
            $combinations->push([
                'options' => $combination,
                'price' => $pricing['final_price'],
                'formatted_price' => $pricing['formatted_price'],
            ]);
        }

        return $combinations->sortBy('price');
    }

    /**
     * Generate all possible option combinations
     */
    private function generateOptionCombinations(Collection $options): array
    {
        if ($options->isEmpty()) {
            return [[]];
        }

        $firstOption = $options->first();
        $remainingOptions = $options->slice(1);
        
        $combinations = [];
        $subCombinations = $this->generateOptionCombinations($remainingOptions);

        foreach ($firstOption->values as $value) {
            foreach ($subCombinations as $subCombination) {
                $combinations[] = array_merge(
                    [$firstOption->id => $value->id],
                    $subCombination
                );
            }
        }

        return $combinations;
    }

    /**
     * Format price for display
     */
    public function formatPrice(float $price): string
    {
        return '₵' . number_format($price, 2);
    }

    /**
     * Calculate order item total
     */
    public function calculateOrderItemTotal(Product $product, array $selectedOptions, int $quantity = 1): array
    {
        $pricing = $this->calculatePrice($product, $selectedOptions);
        $unitPrice = $pricing['final_price'];
        $totalPrice = $unitPrice * $quantity;

        return [
            'unit_price' => $unitPrice,
            'quantity' => $quantity,
            'total_price' => $totalPrice,
            'formatted_unit_price' => $this->formatPrice($unitPrice),
            'formatted_total_price' => $this->formatPrice($totalPrice),
            'pricing_breakdown' => $pricing['breakdown'],
        ];
    }
}
