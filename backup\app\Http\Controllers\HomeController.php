<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Review;
use App\Models\Designer;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page
     */
    public function index()
    {
        // Get featured products (limit to 4 as shown in prototype)
        $featuredProducts = Product::active()
            ->featured()
            ->with(['options.values'])
            ->orderBy('sort_order')
            ->limit(4)
            ->get();

        // Get active reviews for the carousel
        $reviews = Review::active()
            ->orderBy('created_at', 'desc')
            ->get();

        return view('home', compact('featuredProducts', 'reviews'));
    }
}
