@echo off
echo ========================================
echo Fixing Composer Dependencies
echo PrintOnline Ghana Application
echo ========================================
echo.

echo Step 1: Cleaning up corrupted files...
if exist "vendor" (
    echo Removing corrupted vendor directory...
    rmdir /s /q vendor
)
if exist "composer.lock" (
    echo Removing composer.lock...
    del composer.lock
)
echo Cleanup completed!
echo.

echo Step 2: Clearing Composer cache...
composer clear-cache
echo Cache cleared!
echo.

echo Step 3: Installing core dependencies...
composer install --no-dev --optimize-autoloader --no-scripts
if %errorlevel% neq 0 (
    echo ERROR: Core installation failed!
    echo Trying alternative approach...
    composer require laravel/framework:^10.48 --no-scripts
    composer require guzzlehttp/guzzle:^7.2 --no-scripts
    composer require laravel/sanctum:^3.3 --no-scripts
    composer require laravel/tinker:^2.8 --no-scripts
    composer require livewire/livewire:^3.0 --no-scripts
    composer require spatie/laravel-medialibrary:^10.0 --no-scripts
    composer require consoletvs/charts:^6.0 --no-scripts
)
echo Core dependencies installed!
echo.

echo Step 4: Installing Filament...
echo Trying Filament 3.2...
composer require filament/filament:^3.2 --no-scripts
if %errorlevel% neq 0 (
    echo Filament 3.2 failed, trying 3.0...
    composer require filament/filament:^3.0 --no-scripts
    if %errorlevel% neq 0 (
        echo Filament 3.0 failed, trying latest stable...
        composer require filament/filament --no-scripts
    )
)
echo Filament installed!
echo.

echo Step 5: Running Composer scripts...
composer run-script post-autoload-dump
echo Scripts completed!
echo.

echo Step 6: Generating application key...
php artisan key:generate
echo Application key generated!
echo.

echo Step 7: Setting up environment...
if not exist ".env" (
    copy .env.example .env
    echo .env file created!
)
echo.

echo Step 8: Installing Filament panels...
php artisan filament:install --panels
echo Filament panels installed!
echo.

echo ========================================
echo Composer Fix Completed Successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Configure your database in .env file
echo 2. Run: php artisan migrate --seed
echo 3. Run: php artisan storage:link
echo 4. Run: npm install && npm run build
echo.
echo Your application should now work properly!
echo.
pause
