/**
 * Settings Manager Module
 * Handles settings page functionality, tab switching, and auto-save
 * Uses CDN libraries: GSAP and SweetAlert2
 */
class SettingsManager {
    constructor() {
        this.autoSaveTimeout = null;
        this.unsavedChanges = false;
        this.init();
    }

    init() {
        if (this.isSettingsPage()) {
            // Wait for CDN libraries to load
            this.waitForLibraries().then(() => {
                this.setupTabSwitching();
                this.setupAutoSave();
                this.setupFormValidation();
                this.setupUnsavedChangesWarning();
                this.animateInitialLoad();
            });
        }
    }

    /**
     * Wait for CDN libraries to be available
     */
    async waitForLibraries() {
        const checkLibraries = () => {
            return typeof gsap !== 'undefined' && typeof Swal !== 'undefined';
        };

        if (checkLibraries()) {
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            const interval = setInterval(() => {
                if (checkLibraries()) {
                    clearInterval(interval);
                    resolve();
                }
            }, 100);
        });
    }

    /**
     * Check if we're on the settings page
     */
    isSettingsPage() {
        return document.querySelector('.settings-nav-link') !== null;
    }

    /**
     * Setup enhanced tab switching with animations
     */
    setupTabSwitching() {
        const navLinks = document.querySelectorAll('.settings-nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const sectionName = link.getAttribute('href').substring(1);
                this.showSection(sectionName, link);
            });
        });

        // Setup global function for backward compatibility
        window.showSection = (sectionName, clickedElement) => {
            this.showSection(sectionName, clickedElement);
        };
    }

    /**
     * Enhanced section switching with GSAP animations
     */
    showSection(sectionName, clickedElement) {
        const currentSection = document.querySelector('.settings-section:not(.hidden)');
        const targetSection = document.getElementById(sectionName + '-section');
        
        if (!targetSection || targetSection === currentSection) return;

        // Update navigation
        this.updateNavigation(clickedElement);

        // Animate section transition
        if (currentSection) {
            gsap.to(currentSection, {
                duration: 0.3,
                opacity: 0,
                x: -20,
                ease: 'power2.in',
                onComplete: () => {
                    currentSection.classList.add('hidden');
                    this.showTargetSection(targetSection);
                }
            });
        } else {
            this.showTargetSection(targetSection);
        }
    }

    /**
     * Show target section with animation
     */
    showTargetSection(targetSection) {
        targetSection.classList.remove('hidden');
        
        gsap.fromTo(targetSection, 
            {
                opacity: 0,
                x: 20
            },
            {
                duration: 0.4,
                opacity: 1,
                x: 0,
                ease: 'power2.out'
            }
        );
    }

    /**
     * Update navigation styling
     */
    updateNavigation(clickedElement) {
        document.querySelectorAll('.settings-nav-link').forEach(link => {
            link.classList.remove('text-pink-600', 'bg-pink-50');
            link.classList.add('text-gray-600');
        });

        if (clickedElement) {
            clickedElement.classList.remove('text-gray-600');
            clickedElement.classList.add('text-pink-600', 'bg-pink-50');
        }
    }

    /**
     * Setup auto-save functionality
     */
    setupAutoSave() {
        const form = document.querySelector('form');
        if (!form) return;

        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.markUnsavedChanges();
                this.scheduleAutoSave();
            });

            input.addEventListener('change', () => {
                this.markUnsavedChanges();
                this.scheduleAutoSave();
            });
        });
    }

    /**
     * Mark form as having unsaved changes
     */
    markUnsavedChanges() {
        this.unsavedChanges = true;
        this.showUnsavedIndicator();
    }

    /**
     * Show unsaved changes indicator
     */
    showUnsavedIndicator() {
        let indicator = document.querySelector('.unsaved-indicator');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'unsaved-indicator fixed top-4 right-4 bg-yellow-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            indicator.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Unsaved changes';
            document.body.appendChild(indicator);

            gsap.from(indicator, {
                duration: 0.4,
                y: -20,
                opacity: 0,
                ease: 'power2.out'
            });
        }
    }

    /**
     * Hide unsaved changes indicator
     */
    hideUnsavedIndicator() {
        const indicator = document.querySelector('.unsaved-indicator');
        if (indicator) {
            gsap.to(indicator, {
                duration: 0.3,
                y: -20,
                opacity: 0,
                ease: 'power2.in',
                onComplete: () => indicator.remove()
            });
        }
    }

    /**
     * Schedule auto-save
     */
    scheduleAutoSave() {
        if (this.autoSaveTimeout) {
            clearTimeout(this.autoSaveTimeout);
        }

        this.autoSaveTimeout = setTimeout(() => {
            this.performAutoSave();
        }, 3000); // Auto-save after 3 seconds of inactivity
    }

    /**
     * Perform auto-save
     */
    async performAutoSave() {
        const form = document.querySelector('form');
        if (!form || !this.unsavedChanges) return;

        try {
            const formData = new FormData(form);
            
            // Add auto-save flag
            formData.append('auto_save', '1');

            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (response.ok) {
                this.unsavedChanges = false;
                this.hideUnsavedIndicator();
                this.showAutoSaveSuccess();
            } else {
                throw new Error('Auto-save failed');
            }
        } catch (error) {
            console.error('Auto-save error:', error);
            this.showAutoSaveError();
        }
    }

    /**
     * Show auto-save success indicator
     */
    showAutoSaveSuccess() {
        const indicator = document.createElement('div');
        indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        indicator.innerHTML = '<i class="fas fa-check mr-2"></i>Auto-saved';
        document.body.appendChild(indicator);

        gsap.from(indicator, {
            duration: 0.4,
            y: -20,
            opacity: 0,
            ease: 'power2.out'
        });

        setTimeout(() => {
            gsap.to(indicator, {
                duration: 0.3,
                y: -20,
                opacity: 0,
                ease: 'power2.in',
                onComplete: () => indicator.remove()
            });
        }, 2000);
    }

    /**
     * Show auto-save error indicator
     */
    showAutoSaveError() {
        const indicator = document.createElement('div');
        indicator.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        indicator.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Auto-save failed';
        document.body.appendChild(indicator);

        gsap.from(indicator, {
            duration: 0.4,
            y: -20,
            opacity: 0,
            ease: 'power2.out'
        });

        setTimeout(() => {
            gsap.to(indicator, {
                duration: 0.3,
                y: -20,
                opacity: 0,
                ease: 'power2.in',
                onComplete: () => indicator.remove()
            });
        }, 3000);
    }

    /**
     * Setup form validation
     */
    setupFormValidation() {
        const form = document.querySelector('form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            if (!this.validateSettingsForm()) {
                e.preventDefault();
            } else {
                this.unsavedChanges = false;
                this.hideUnsavedIndicator();
            }
        });
    }

    /**
     * Validate settings form
     */
    validateSettingsForm() {
        let isValid = true;
        const errors = [];

        // Validate email fields
        const emailFields = document.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                errors.push(`${field.labels[0]?.textContent || 'Email field'} must be a valid email address`);
                isValid = false;
            }
        });

        // Validate URL fields
        const urlFields = document.querySelectorAll('input[type="url"]');
        urlFields.forEach(field => {
            if (field.value && !this.isValidUrl(field.value)) {
                errors.push(`${field.labels[0]?.textContent || 'URL field'} must be a valid URL`);
                isValid = false;
            }
        });

        // Validate numeric fields
        const numberFields = document.querySelectorAll('input[type="number"]');
        numberFields.forEach(field => {
            if (field.value && (isNaN(field.value) || parseFloat(field.value) < 0)) {
                errors.push(`${field.labels[0]?.textContent || 'Number field'} must be a valid positive number`);
                isValid = false;
            }
        });

        if (!isValid) {
            window.showToast('error', 'Please fix the following errors: ' + errors.join(', '));
        }

        return isValid;
    }

    /**
     * Setup unsaved changes warning
     */
    setupUnsavedChangesWarning() {
        window.addEventListener('beforeunload', (e) => {
            if (this.unsavedChanges) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                return e.returnValue;
            }
        });
    }

    /**
     * Animate initial page load
     */
    animateInitialLoad() {
        const sections = document.querySelectorAll('.settings-section');
        const navLinks = document.querySelectorAll('.settings-nav-link');

        // Animate navigation
        gsap.from(navLinks, {
            duration: 0.6,
            x: -20,
            opacity: 0,
            stagger: 0.1,
            ease: 'power2.out'
        });

        // Animate visible section
        const visibleSection = document.querySelector('.settings-section:not(.hidden)');
        if (visibleSection) {
            gsap.from(visibleSection, {
                duration: 0.8,
                y: 20,
                opacity: 0,
                ease: 'power2.out',
                delay: 0.3
            });
        }
    }

    /**
     * Validate email format
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validate URL format
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }
}

// Initialize settings manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.SettingsManager = new SettingsManager();
});

export default SettingsManager;
