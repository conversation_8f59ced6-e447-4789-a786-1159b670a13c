<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
    ];

    protected $attributes = [
        'type' => 'string',
    ];

    /**
     * Scope a query to filter by group.
     */
    public function scopeGroup(Builder $query, string $group): void
    {
        $query->where('group', $group);
    }

    /**
     * Get a setting value by key
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }

        return static::castValue($setting->value, $setting->type);
    }

    /**
     * Set a setting value
     */
    public static function set(string $key, $value, string $type = 'string'): void
    {
        static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
            ]
        );
    }

    /**
     * Cast value to appropriate type
     */
    protected static function castValue($value, ?string $type = null)
    {
        if (!$type) {
            return $value;
        }

        return match($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json' => json_decode($value, true),
            default => $value,
        };
    }

    /**
     * Get the casted value attribute
     */
    public function getValueAttribute($value)
    {
        return static::castValue($value, $this->type);
    }

    /**
     * Set the value attribute
     */
    public function setValueAttribute($value): void
    {
        if (!$this->type) {
            $this->attributes['value'] = (string) $value;
            return;
        }

        $this->attributes['value'] = match($this->type) {
            'json' => json_encode($value),
            'boolean' => $value ? '1' : '0',
            default => (string) $value,
        };
    }
}
