import './bootstrap';
import Alpine from 'alpinejs';
import { Uppy } from '@uppy/core';
import DragDrop from '@uppy/drag-drop';
import ProgressBar from '@uppy/progress-bar';
import StatusBar from '@uppy/status-bar';
import ThumbnailGenerator from '@uppy/thumbnail-generator';

// Import admin modules (these will use CDN libraries)
import './admin/admin-core';
import './admin/product-manager';
import './admin/settings-manager';

// Make Alpine available globally
window.Alpine = Alpine;

// Make Uppy available globally for Livewire components
window.Uppy = Uppy;
window.UppyDragDrop = DragDrop;
window.UppyProgressBar = ProgressBar;
window.UppyStatusBar = StatusBar;
window.UppyThumbnailGenerator = ThumbnailGenerator;

// Start Alpine
Alpine.start();

// Product pricing functionality
window.updateProductPrice = function(productData, selectedOptions) {
    let currentPrice = 0;
    let multiplier = 1;

    selectedOptions.forEach((optionValue, optionIndex) => {
        const option = productData.options[optionIndex];
        const value = option.values[optionValue];

        if (option.type === 'quantity') {
            multiplier = value.multiplier;
        } else {
            currentPrice += value.price;
        }
    });

    return currentPrice * multiplier;
};
