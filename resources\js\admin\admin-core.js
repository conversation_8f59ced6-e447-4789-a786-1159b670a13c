import { gsap } from 'gsap';
import Swal from 'sweetalert2';

/**
 * Admin Core Module
 * Handles common admin functionality, animations, and utilities
 */
class AdminCore {
    constructor() {
        this.init();
    }

    init() {
        this.setupGlobalAnimations();
        this.setupNotifications();
        this.setupFormValidation();
        this.setupLoadingStates();
        this.setupConfirmDialogs();
    }

    /**
     * Setup global GSAP animations
     */
    setupGlobalAnimations() {
        // Animate page load
        gsap.from('.admin-content', {
            duration: 0.6,
            y: 20,
            opacity: 0,
            ease: 'power2.out'
        });

        // Animate cards on hover
        document.querySelectorAll('.hover\\:shadow-lg').forEach(card => {
            card.addEventListener('mouseenter', () => {
                gsap.to(card, {
                    duration: 0.3,
                    y: -2,
                    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    ease: 'power2.out'
                });
            });

            card.addEventListener('mouseleave', () => {
                gsap.to(card, {
                    duration: 0.3,
                    y: 0,
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                    ease: 'power2.out'
                });
            });
        });

        // Animate sidebar links
        document.querySelectorAll('.sidebar-link').forEach(link => {
            link.addEventListener('mouseenter', () => {
                gsap.to(link, {
                    duration: 0.2,
                    x: 5,
                    ease: 'power2.out'
                });
            });

            link.addEventListener('mouseleave', () => {
                gsap.to(link, {
                    duration: 0.2,
                    x: 0,
                    ease: 'power2.out'
                });
            });
        });
    }

    /**
     * Setup notification system
     */
    setupNotifications() {
        // Configure SweetAlert2 defaults
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer);
                toast.addEventListener('mouseleave', Swal.resumeTimer);
            }
        });

        window.showToast = (type, message) => {
            Toast.fire({
                icon: type,
                title: message
            });
        };

        // Show Laravel flash messages as toasts
        const flashMessages = document.querySelectorAll('[data-flash-message]');
        flashMessages.forEach(message => {
            const type = message.dataset.flashType || 'info';
            const text = message.dataset.flashMessage;
            window.showToast(type, text);
            message.remove();
        });
    }

    /**
     * Setup form validation
     */
    setupFormValidation() {
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        this.showFieldError(field, 'This field is required');
                        isValid = false;
                    } else {
                        this.clearFieldError(field);
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    window.showToast('error', 'Please fill in all required fields');
                }
            });
        });
    }

    /**
     * Setup loading states for buttons
     */
    setupLoadingStates() {
        document.querySelectorAll('button[type="submit"]').forEach(button => {
            button.addEventListener('click', () => {
                if (button.form && button.form.checkValidity()) {
                    this.setButtonLoading(button);
                }
            });
        });
    }

    /**
     * Setup confirmation dialogs
     */
    setupConfirmDialogs() {
        document.querySelectorAll('[data-confirm]').forEach(element => {
            element.addEventListener('click', (e) => {
                e.preventDefault();
                const message = element.dataset.confirm;
                
                Swal.fire({
                    title: 'Are you sure?',
                    text: message,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc2626',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Yes, proceed!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        if (element.tagName === 'A') {
                            window.location.href = element.href;
                        } else if (element.form) {
                            element.form.submit();
                        }
                    }
                });
            });
        });
    }

    /**
     * Show field error
     */
    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-red-500 text-sm mt-1 field-error';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
        
        gsap.from(errorDiv, {
            duration: 0.3,
            opacity: 0,
            y: -10,
            ease: 'power2.out'
        });
    }

    /**
     * Clear field error
     */
    clearFieldError(field) {
        field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Set button loading state
     */
    setButtonLoading(button) {
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
        
        // Store original text for restoration
        button.dataset.originalText = originalText;
        
        // Auto-restore after 10 seconds as fallback
        setTimeout(() => {
            this.clearButtonLoading(button);
        }, 10000);
    }

    /**
     * Clear button loading state
     */
    clearButtonLoading(button) {
        button.disabled = false;
        if (button.dataset.originalText) {
            button.innerHTML = button.dataset.originalText;
            delete button.dataset.originalText;
        }
    }

    /**
     * Animate element entrance
     */
    animateIn(element, options = {}) {
        const defaults = {
            duration: 0.6,
            y: 20,
            opacity: 0,
            ease: 'power2.out'
        };
        
        gsap.from(element, { ...defaults, ...options });
    }

    /**
     * Animate element exit
     */
    animateOut(element, options = {}) {
        const defaults = {
            duration: 0.4,
            y: -20,
            opacity: 0,
            ease: 'power2.in'
        };
        
        return gsap.to(element, { ...defaults, ...options });
    }
}

// Initialize admin core when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.AdminCore = new AdminCore();
});

export default AdminCore;
