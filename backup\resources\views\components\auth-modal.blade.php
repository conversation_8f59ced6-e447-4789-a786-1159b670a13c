<!-- Auth Modal -->
<div id="auth-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="bg-white p-8 rounded-xl w-90 max-w-md mx-4 relative">
        <!-- Close Button -->
        <button onclick="hideAuthModal()" class="absolute top-4 right-4 text-gray-500 hover:text-gray-800 text-xl">
            &times;
        </button>

        <!-- Login Form -->
        <div id="login-form-container">
            <h2 class="text-2xl font-bold text-center mb-6">Login</h2>
            <form id="login-form" method="POST" action="{{ route('login') }}" class="space-y-4">
                @csrf
                <div>
                    <input type="email" name="email" placeholder="Email Address" 
                           class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500" 
                           required value="{{ old('email') }}">
                    @error('email')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <input type="password" name="password" placeholder="Password" 
                           class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500" 
                           required>
                    @error('password')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" name="remember" class="rounded border-gray-300 text-pink-600 shadow-sm focus:ring-pink-500">
                        <span class="ml-2 text-sm text-gray-600">Remember me</span>
                    </label>
                    <a href="{{ route('password.request') }}" class="text-sm text-pink-500 hover:text-pink-600">
                        Forgot password?
                    </a>
                </div>

                <button type="submit" class="w-full bg-pink-500 text-white font-bold py-3 rounded-lg hover:bg-pink-600 transition">
                    Login
                </button>
                
                <p class="text-center text-sm">
                    Don't have an account? 
                    <a href="#" onclick="showSignupForm()" class="font-semibold text-pink-500 hover:text-pink-600">Sign Up</a>
                </p>
            </form>
        </div>

        <!-- Signup Form -->
        <div id="signup-form-container" class="hidden">
            <h2 class="text-2xl font-bold text-center mb-6">Create Account</h2>
            <form id="signup-form" method="POST" action="{{ route('register') }}" class="space-y-4">
                @csrf
                <div>
                    <input type="text" name="name" placeholder="Full Name" 
                           class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500" 
                           required value="{{ old('name') }}">
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <input type="email" name="email" placeholder="Email Address" 
                           class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500" 
                           required value="{{ old('email') }}">
                    @error('email')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <input type="password" name="password" placeholder="Password" 
                           class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500" 
                           required>
                    @error('password')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <input type="password" name="password_confirmation" placeholder="Confirm Password" 
                           class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500" 
                           required>
                </div>

                <button type="submit" class="w-full bg-pink-500 text-white font-bold py-3 rounded-lg hover:bg-pink-600 transition">
                    Create Account
                </button>
                
                <p class="text-center text-sm">
                    Already have an account? 
                    <a href="#" onclick="showLoginForm()" class="font-semibold text-pink-500 hover:text-pink-600">Login</a>
                </p>
            </form>
        </div>
    </div>
</div>

<script>
function showSignupForm() {
    document.getElementById('login-form-container').classList.add('hidden');
    document.getElementById('signup-form-container').classList.remove('hidden');
}

function showLoginForm() {
    document.getElementById('signup-form-container').classList.add('hidden');
    document.getElementById('login-form-container').classList.remove('hidden');
}

// Show auth modal if there are validation errors
@if($errors->any())
    document.addEventListener('DOMContentLoaded', function() {
        showAuthModal();
        @if(old('name'))
            showSignupForm();
        @endif
    });
@endif
</script>
