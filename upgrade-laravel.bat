@echo off
echo ========================================
echo Laravel 10 Upgrade Script
echo PrintOnline Ghana Application
echo ========================================
echo.

echo Step 1: Backing up current installation...
if not exist "backup" mkdir backup
xcopy /E /I /Y "app" "backup\app" >nul
xcopy /E /I /Y "config" "backup\config" >nul
xcopy /E /I /Y "database" "backup\database" >nul
xcopy /E /I /Y "resources" "backup\resources" >nul
xcopy /E /I /Y "routes" "backup\routes" >nul
copy ".env" "backup\.env" >nul 2>&1
echo Backup completed!
echo.

echo Step 2: Clearing Laravel caches...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
echo Caches cleared!
echo.

echo Step 3: Updating Composer dependencies...
composer update --no-dev --optimize-autoloader
if %errorlevel% neq 0 (
    echo ERROR: Composer update failed!
    echo Please check the error messages above.
    pause
    exit /b 1
)
echo Dependencies updated!
echo.

echo Step 4: Publishing Filament assets...
php artisan filament:install --panels
php artisan vendor:publish --tag=filament-config
echo Filament assets published!
echo.

echo Step 5: Running database migrations...
php artisan migrate --force
echo Migrations completed!
echo.

echo Step 6: Rebuilding application cache...
php artisan config:cache
php artisan route:cache
php artisan view:cache
echo Caches rebuilt!
echo.

echo Step 7: Installing and building frontend assets...
call npm install
call npm run build
echo Frontend assets built!
echo.

echo Step 8: Creating storage link...
php artisan storage:link
echo Storage link created!
echo.

echo ========================================
echo Laravel 10 Upgrade Completed Successfully!
echo ========================================
echo.
echo Your application has been upgraded to Laravel 10.48
echo with Filament 3.2 admin panel.
echo.
echo You can now access:
echo - Frontend: http://localhost/Printshop/public/
echo - Admin: http://localhost/Printshop/public/admin
echo.
echo Default admin credentials:
echo Email: <EMAIL>
echo Password: password
echo.
pause
