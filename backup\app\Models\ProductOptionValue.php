<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductOptionValue extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_option_id',
        'name',
        'value',
        'price',
        'multiplier',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'multiplier' => 'decimal:2',
    ];

    /**
     * Get the option that owns this value
     */
    public function option()
    {
        return $this->belongsTo(ProductOption::class, 'product_option_id');
    }

    /**
     * Get the formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return '₵' . number_format($this->price, 2);
    }

    /**
     * Check if this value has a color
     */
    public function hasColor(): bool
    {
        return !empty($this->value) && $this->option->isColorOption();
    }

    /**
     * Get the color value for CSS
     */
    public function getColorValueAttribute(): ?string
    {
        return $this->hasColor() ? $this->value : null;
    }
}
