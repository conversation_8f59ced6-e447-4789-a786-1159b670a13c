<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing settings with null type to have 'string' type
        DB::table('settings')
            ->whereNull('type')
            ->update(['type' => 'string']);

        // Update specific boolean settings
        $booleanSettings = [
            'email_notifications',
            'enable_paystack',
            'enable_momo',
            'enable_bank_transfer'
        ];

        foreach ($booleanSettings as $key) {
            DB::table('settings')
                ->where('key', $key)
                ->update(['type' => 'boolean']);
        }

        // Update numeric settings
        $numericSettings = [
            'shipping_fee_accra',
            'shipping_fee_other',
            'free_shipping_threshold'
        ];

        foreach ($numericSettings as $key) {
            DB::table('settings')
                ->where('key', $key)
                ->update(['type' => 'float']);
        }

        // Update integer settings
        $integerSettings = [
            'processing_time'
        ];

        foreach ($integerSettings as $key) {
            DB::table('settings')
                ->where('key', $key)
                ->update(['type' => 'integer']);
        }

        // Make type column not nullable with default
        Schema::table('settings', function (Blueprint $table) {
            $table->string('type')->default('string')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->string('type')->nullable()->change();
        });
    }
};
