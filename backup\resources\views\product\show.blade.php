<x-layouts.app :title="$product->name . ' - PrintOnline Ghana'">
    <div class="bg-white min-h-screen">
        <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Breadcrumbs -->
            <nav class="text-sm text-gray-500 mb-8">
                <a href="{{ route('home') }}" class="hover:text-gray-800">Home</a>
                <span class="mx-2">/</span>
                <a href="{{ route('shop') }}?category={{ $product->category }}" class="hover:text-gray-800">{{ $product->category }}</a>
                <span class="mx-2">/</span>
                <span class="font-medium text-gray-800">{{ $product->name }}</span>
            </nav>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Left Column: Product Image -->
                <div class="bg-orange-200 rounded-2xl flex items-center justify-center p-8">
                    <img id="product-image" 
                         src="{{ $product->image_url }}" 
                         alt="{{ $product->name }}" 
                         class="max-w-full h-auto">
                </div>

                <!-- Right Column: Product Details -->
                <div>
                    <h1 class="text-4xl font-bold">{{ $product->name }}</h1>
                    <p class="text-gray-500 mt-2">By PrintOnline Ghana</p>
                    
                    <div class="flex items-center mt-4">
                        <p id="product-price" class="text-3xl font-bold text-orange-500">
                            {{ $defaultPricing['formatted_price'] }}
                        </p>
                    </div>

                    <p class="mt-6 text-gray-600">{{ $product->description }}</p>

                    <form id="order-form" method="POST" action="{{ route('orders.store') }}" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="product_id" value="{{ $product->id }}">

                        <!-- Product Options -->
                        <div id="product-options" class="mt-8 space-y-6">
                            @foreach($product->options as $optionIndex => $option)
                                <div>
                                    <h3 class="text-md font-semibold text-gray-800 mb-3">{{ $option->name }}</h3>
                                    <div class="flex items-center gap-3 flex-wrap">
                                        @foreach($option->values as $valueIndex => $value)
                                            @if($option->type === 'color')
                                                <button type="button" 
                                                        class="option-btn w-6 h-6 rounded-full border-2 {{ $valueIndex === 0 ? 'selected ring-2 ring-gray-800' : 'border-gray-300' }}"
                                                        style="background-color: {{ $value->value }}"
                                                        data-option-id="{{ $option->id }}"
                                                        data-value-id="{{ $value->id }}"
                                                        data-option-index="{{ $optionIndex }}"
                                                        data-value-index="{{ $valueIndex }}"
                                                        title="{{ $value->name }}">
                                                </button>
                                            @else
                                                <button type="button" 
                                                        class="option-btn {{ $valueIndex === 0 ? 'selected' : '' }}"
                                                        data-option-id="{{ $option->id }}"
                                                        data-value-id="{{ $value->id }}"
                                                        data-option-index="{{ $optionIndex }}"
                                                        data-value-index="{{ $valueIndex }}">
                                                    {{ $value->name }}
                                                </button>
                                            @endif
                                            <input type="hidden" 
                                                   name="options[{{ $option->id }}]" 
                                                   value="{{ $valueIndex === 0 ? $value->id : '' }}"
                                                   id="option-{{ $option->id }}">
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Quantity -->
                        <div class="mt-6">
                            <h3 class="text-md font-semibold text-gray-800 mb-3">Quantity</h3>
                            <input type="number" 
                                   name="quantity" 
                                   value="1" 
                                   min="1" 
                                   max="100"
                                   class="quantity-input w-20 text-center border border-gray-300 rounded-lg py-2 px-3 focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        </div>

                        <!-- File Upload -->
                        <div class="mt-8">
                            <h3 class="text-md font-semibold text-gray-800 mb-3">Upload Your Design</h3>
                            <div id="uppy-drag-drop"></div>
                            <div id="uppy-progress"></div>
                            <div id="uppy-status"></div>
                            <input type="file" 
                                   name="design_file" 
                                   accept="image/*,.pdf,.svg"
                                   class="mt-4 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100">
                            <p class="text-xs text-gray-500 mt-2">
                                Supported formats: JPG, PNG, GIF, PDF, SVG (Max: 10MB)
                            </p>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="flex items-center mt-8 gap-4">
                            @auth
                                <button type="submit" 
                                        class="flex-grow bg-orange-500 text-white font-bold py-4 rounded-lg hover:bg-orange-600 transition font-heading">
                                    Print
                                </button>
                            @else
                                <button type="button" 
                                        onclick="showAuthModal()"
                                        class="flex-grow bg-orange-500 text-white font-bold py-4 rounded-lg hover:bg-orange-600 transition font-heading">
                                    Print
                                </button>
                            @endauth
                        </div>
                        
                        <!-- Payment Methods -->
                        <div class="mt-8 border-t pt-6">
                            <h3 class="font-bold text-center">Guaranteed safe checkout</h3>
                            <div class="flex justify-center items-center gap-4 mt-4 text-3xl text-gray-400">
                                <i class="fab fa-cc-paypal"></i>
                                <i class="fab fa-cc-visa"></i>
                                <i class="fab fa-cc-mastercard"></i>
                                <i class="fab fa-cc-amex"></i>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Product data for JavaScript
        const productData = @json([
            'id' => $product->id,
            'name' => $product->name,
            'options' => $product->options->map(function($option) {
                return [
                    'id' => $option->id,
                    'name' => $option->name,
                    'type' => $option->type,
                    'values' => $option->values->map(function($value) {
                        return [
                            'id' => $value->id,
                            'name' => $value->name,
                            'value' => $value->value,
                            'price' => $value->price,
                            'multiplier' => $value->multiplier,
                        ];
                    })
                ];
            })
        ]);

        // Handle option selection
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('option-btn')) {
                const btn = e.target;
                const optionId = btn.dataset.optionId;
                const valueId = btn.dataset.valueId;
                
                // Remove selected class from siblings
                const siblings = btn.parentElement.querySelectorAll('.option-btn');
                siblings.forEach(sibling => {
                    sibling.classList.remove('selected');
                    if (sibling.classList.contains('ring-2')) {
                        sibling.classList.remove('ring-2', 'ring-gray-800');
                    }
                });
                
                // Add selected class to clicked button
                btn.classList.add('selected');
                if (btn.style.backgroundColor) { // Color option
                    btn.classList.add('ring-2', 'ring-gray-800');
                }
                
                // Update hidden input
                document.getElementById(`option-${optionId}`).value = valueId;
                
                // Update price
                updatePrice();
            }
        });

        // Update price function
        function updatePrice() {
            const selectedOptions = {};
            
            // Get all selected options
            productData.options.forEach(option => {
                const input = document.getElementById(`option-${option.id}`);
                if (input && input.value) {
                    selectedOptions[option.id] = parseInt(input.value);
                }
            });

            // Calculate price via API
            fetch(`{{ route('product.price', $product) }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    options: selectedOptions
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('product-price').textContent = data.pricing.formatted_price;
                }
            })
            .catch(error => {
                console.error('Error calculating price:', error);
            });
        }

        // Initialize Uppy for file upload (optional enhancement)
        // This would require Uppy to be properly loaded
        
        // Form validation
        document.getElementById('order-form').addEventListener('submit', function(e) {
            // Check if all required options are selected
            let allSelected = true;
            productData.options.forEach(option => {
                const input = document.getElementById(`option-${option.id}`);
                if (!input.value) {
                    allSelected = false;
                }
            });

            if (!allSelected) {
                e.preventDefault();
                alert('Please select all product options before placing your order.');
                return false;
            }
        });
    </script>
    @endpush
</x-layouts.app>
