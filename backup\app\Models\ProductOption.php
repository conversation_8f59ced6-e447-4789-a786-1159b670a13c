<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductOption extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'name',
        'type',
        'sort_order',
    ];

    /**
     * Get the product that owns this option
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the option values
     */
    public function values()
    {
        return $this->hasMany(ProductOptionValue::class)->orderBy('sort_order');
    }

    /**
     * Check if this is a color option
     */
    public function isColorOption(): bool
    {
        return $this->type === 'color';
    }

    /**
     * Check if this is a quantity option
     */
    public function isQuantityOption(): bool
    {
        return $this->type === 'quantity';
    }

    /**
     * Check if this is a button option
     */
    public function isButtonOption(): bool
    {
        return $this->type === 'button';
    }
}
